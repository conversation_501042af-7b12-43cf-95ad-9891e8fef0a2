package model;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class Produk extends Database {
    private String idProduk;
    private String namaProduk;
    private int harga;
    private String deskripsi;
    private int stok;

    public String getIdProduk() {
        return idProduk;
    }

    public void setIdProduk(String idProduk) {
        this.idProduk = idProduk;
    }

    public String getNamaProduk() {
        return namaProduk;
    }

    public void setNamaProduk(String namaProduk) {
        this.namaProduk = namaProduk;
    }

    public int getHarga() {
        return harga;
    }

    public void setHarga(int harga) {
        this.harga = harga;
    }

    public String getDeskripsi() {
        return deskripsi;
    }

    public void setDeskripsi(String deskripsi) {
        this.deskripsi = deskripsi;
    }

    public int getStok() {
        return stok;
    }

    public void setStok(int stok) {
        this.stok = stok;
    }

    public boolean simpanProduk(Produk data) throws SQLException {
        String sql = "INSERT INTO produk (id_produk, nama_produk, harga, deskripsi, stok) VALUES (?, ?, ?, ?, ?)";
        try (Connection conn = Database.configDB(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, data.getIdProduk());
            pstmt.setString(2, data.getNamaProduk());
            pstmt.setInt(3, data.getHarga());
            pstmt.setString(4, data.getDeskripsi());
            pstmt.setInt(5, data.getStok());
            pstmt.execute();
            return true;
        } catch (SQLException e) {
            System.err.println("Simpan Produk Error: " + e);
            return false;
        }
    }

    public boolean updateProduk(Produk data) throws SQLException {
        String sql = "UPDATE produk SET nama_produk = ?, harga = ?, deskripsi = ?, stok = ? WHERE id_produk = ?";
        try (Connection conn = Database.configDB(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, data.getNamaProduk());
            pstmt.setInt(2, data.getHarga());
            pstmt.setString(3, data.getDeskripsi());
            pstmt.setInt(4, data.getStok());
            pstmt.setString(5, data.getIdProduk());
            pstmt.executeUpdate();
            return true;
        } catch (SQLException e) {
            System.err.println("Update Produk Error: " + e);
            return false;
        }
    }

    public boolean hapusProduk(Produk data) throws SQLException {
        String sql = "DELETE FROM produk WHERE id_produk = ?";
        try (Connection conn = Database.configDB(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, data.getIdProduk());
            pstmt.executeUpdate();
            return true;
        } catch (SQLException e) {
            System.err.println("Hapus Produk Error: " + e);
            return false;
        }
    }

    public int getHargaProduk(String idProduk) throws SQLException {
        String sql = "SELECT harga FROM produk WHERE id_produk = ?";
        try (Connection conn = Database.configDB(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, idProduk);
            ResultSet rs = pstmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("harga");
            }
        }
        return 0;
    }

    public int getStokProduk(String idProduk) throws SQLException {
        String sql = "SELECT stok FROM produk WHERE id_produk = ?";
        try (Connection conn = Database.configDB(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, idProduk);
            ResultSet rs = pstmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("stok");
            }
        }
        return 0;
    }

    public boolean kurangiStok(String idProduk, int jumlah) throws SQLException {
        String sql = "UPDATE produk SET stok = stok - ? WHERE id_produk = ?";
        try (Connection conn = Database.configDB(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setInt(1, jumlah);
            pstmt.setString(2, idProduk);
            int affected = pstmt.executeUpdate();
            return affected > 0;
        }
    }
    public int getHargaProdukById(String idProduk) throws SQLException {
    Connection conn = Database.configDB();
    String sql = "SELECT harga FROM produk WHERE id_produk = ?";
    PreparedStatement ps = conn.prepareStatement(sql);
    ps.setString(1, idProduk);
    ResultSet rs = ps.executeQuery();
    if (rs.next()) {
        // Ambil harga sebagai Number lalu konversi ke int
        Number harga = (Number) rs.getObject("harga");
        return harga.intValue();
    } else {
        throw new SQLException("Produk tidak ditemukan");
    }
}
}
