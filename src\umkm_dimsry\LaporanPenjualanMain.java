package umkm_dimsry;

import view.LaporanPenjualan;
import model.LaporanPenjualanModel;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.time.LocalDate;

public class LaporanPenjualanMain {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
 
                LaporanPenjualan frame = new LaporanPenjualan();
                frame.setTitle("Laporan Penjualan Harian");
                frame.setLocationRelativeTo(null);
                frame.setVisible(true);

                String today = LocalDate.now().toString(); 

                LaporanPenjualanModel model = new LaporanPenjualanModel(); 
                DefaultTableModel tableModel = model.getLaporanPenjualanHarian(today);

                frame.setLaporanPenjualanTable(tableModel);

            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "Gagal menampilkan laporan: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
