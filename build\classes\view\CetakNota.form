<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="103" alignment="0" groupAlignment="1" attributes="0">
                      <Component id="TotalHargaField" alignment="1" min="-2" pref="121" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="notaScrollPane" min="-2" max="-2" attributes="0"/>
                              <EmptySpace type="unrelated" max="-2" attributes="0"/>
                              <Component id="jScrollPane1" min="-2" pref="486" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace min="-2" pref="29" max="-2" attributes="0"/>
                              <Component id="namaField" min="-2" pref="117" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace min="-2" pref="104" max="-2" attributes="0"/>
                              <Component id="CetakNotaText" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace min="-2" pref="156" max="-2" attributes="0"/>
                              <Component id="alamatLabel" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace min="-2" pref="211" max="-2" attributes="0"/>
                              <Component id="noHpLabel" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace min="-2" pref="154" max="-2" attributes="0"/>
                              <Group type="103" groupAlignment="1" attributes="0">
                                  <Component id="trimakasiLabel" min="-2" max="-2" attributes="0"/>
                                  <Component id="dimsumLabel" min="-2" max="-2" attributes="0"/>
                              </Group>
                          </Group>
                      </Group>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="204" max="-2" attributes="0"/>
                      <Component id="bukaLabel" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace pref="31" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" pref="244" max="-2" attributes="0"/>
                      <Component id="notaScrollPane" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="CetakNotaText" min="-2" max="-2" attributes="0"/>
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Component id="alamatLabel" min="-2" pref="16" max="-2" attributes="0"/>
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Component id="noHpLabel" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="32" max="-2" attributes="0"/>
                      <Component id="namaField" min="-2" pref="31" max="-2" attributes="0"/>
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Component id="jScrollPane1" min="-2" pref="138" max="-2" attributes="0"/>
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Component id="TotalHargaField" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="42" max="-2" attributes="0"/>
                      <Component id="trimakasiLabel" min="-2" max="-2" attributes="0"/>
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Component id="bukaLabel" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Component id="dimsumLabel" min="-2" max="-2" attributes="0"/>
              <EmptySpace pref="44" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="CetakNotaText">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="24" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Selamat Datang di DIMSRY"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="namaField">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Nama"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="TotalHargaField">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Harga Produk"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="alamatLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Jln. Garuda Sakti Km 03 Pekanbaru"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="noHpLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="081397234856"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="trimakasiLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Terimakasih Telah Berbelanja di DIMSRY"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="bukaLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Buka : 09:00 - 21:00"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="dimsumLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Dimsum paling enak no.1 se-Indonesia "/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="notaTable">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Tahoma" size="14" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="4" rowCount="4">
                <Column editable="true" title="Title 1" type="java.lang.Object"/>
                <Column editable="true" title="Title 2" type="java.lang.Object"/>
                <Column editable="true" title="Title 3" type="java.lang.Object"/>
                <Column editable="true" title="Title 4" type="java.lang.Object"/>
              </Table>
            </Property>
          </Properties>
          <AuxValues>
            <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
          </AuxValues>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JScrollPane" name="notaScrollPane">
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
    </Container>
  </SubComponents>
</Form>
