package controller;
import model.Transaksi;
import model.Produk;
import view.InputTransaksiPenjualan;
import view.CetakNota;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.event.*;
import java.sql.SQLException;
import java.util.ArrayList;

public class TransaksiController {
    private InputTransaksiPenjualan view;
    private ArrayList<Transaksi> keranjang = new ArrayList<>();

    public TransaksiController(InputTransaksiPenjualan view) {
        this.view = view;

        view.keranjangTabel.setModel(new DefaultTableModel(
            new Object[]{"ID Produk", "Nama", "Harga", "Jumlah", "Subtotal"}, 0
        ));

        view.tambahButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                tambahKeKeranjang();
            }
        });

        view.selesaiButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                selesaiTransaksi();
            }
        });
    }

    private void tambahKeKeranjang() {
        String idProduk = view.produkField.getText().trim();
        String namaPembeli = view.namaProdukField.getText().trim();

        if (idProduk.isEmpty() || namaPembeli.isEmpty()) {
            JOptionPane.showMessageDialog(view, "Nama dan ID Produk harus diisi!");
            return;
        }

        int jumlah;
        try {
            jumlah = Integer.parseInt(JOptionPane.showInputDialog("Masukkan jumlah produk:"));
            if (jumlah <= 0) throw new NumberFormatException();
        } catch (NumberFormatException ex) {
            JOptionPane.showMessageDialog(view, "Jumlah harus berupa angka lebih dari 0.");
            return;
        }

        try {
            Produk produk = new Produk();
            int harga = produk.getHargaProdukById(idProduk);
            int subtotal = harga * jumlah;

            Transaksi t = new Transaksi();
            t.setNama(namaPembeli);
            t.setId_produk(idProduk);
            t.setJumlah(jumlah);
            t.setHarga_satuan(harga);
            t.setSubtotal(subtotal);
            keranjang.add(t);

            DefaultTableModel model = (DefaultTableModel) view.keranjangTabel.getModel();
            model.addRow(new Object[]{idProduk, namaPembeli, harga, jumlah, subtotal});

            JOptionPane.showMessageDialog(view, "Berhasil ditambahkan ke keranjang.");
            view.produkField.setText("");
view.namaProdukField.setText("");
        } catch (Exception ex) {
            JOptionPane.showMessageDialog(view, "Gagal mengambil harga produk: " + ex.getMessage());
        }
    }

    private void selesaiTransaksi() {
        if (keranjang.isEmpty()) {
            JOptionPane.showMessageDialog(view, "Keranjang masih kosong.");
            return;
        }

        int total = 0;
        try {
            Transaksi model = new Transaksi();
            String nama = keranjang.get(0).getNama();
            String status = model.getStatusMemberByNama(nama);

            for (Transaksi t : keranjang) {
                total += t.getSubtotal();
                model.simpanTransaksi(t);
            }

            if (status.equalsIgnoreCase("member")) {
                total -= 5000;
                if (total < 0) total = 0;
            }

            view.totalHargaField.setText("Total Harga: " + total);
            JOptionPane.showMessageDialog(view, "Transaksi berhasil disimpan.");

            CetakNota nota = new CetakNota();
            nota.setLocationRelativeTo(view);
            nota.namaField.setText(nama);
            nota.TotalHargaField.setText("Rp " + total);

            DefaultTableModel notaModel = (DefaultTableModel) nota.notaTable.getModel();
            for (Transaksi t : keranjang) {
                notaModel.addRow(new Object[]{
                    t.getId_produk(),
                    "Rp " + t.getHarga_satuan(),
                    t.getJumlah(),
                    "Rp " + t.getSubtotal()
                });
            }

            nota.setVisible(true);

            keranjang.clear();
            DefaultTableModel modelTabel = (DefaultTableModel) view.keranjangTabel.getModel();
            modelTabel.setRowCount(0);
            view.simpanButton.setEnabled(false);

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(view, "Gagal menyimpan transaksi: " + e.getMessage());
        }
    }
}
