package controller;
import model.Database;
import view.LaporanPenjualan;

import javax.swing.table.DefaultTableModel;
import java.sql.*;

public class LaporanPenjualanController {

    private LaporanPenjualan view;

    public LaporanPenjualanController(LaporanPenjualan view) {
        this.view = view;
        tampilkanLaporanHarian();
    }

    public void tampilkanLaporanHarian() {
        DefaultTableModel model = new DefaultTableModel();
        model.addColumn("ID Transaksi");
        model.addColumn("Nama Produk");
        model.addColumn("Tanggal");
        model.addColumn("Subtotal");

        try {
            String query = "SELECT t.id_transaksi, p.nama_produk, t.tanggal_transaksi, dt.subtotal " +
                    "FROM transaksi t " +
                    "JOIN detail_transaksi dt ON t.id_transaksi = dt.id_transaksi " +
                    "JOIN produk p ON dt.id_produk = p.id_produk " +
                    "WHERE DATE(t.tanggal_transaksi) = CURDATE()"; 
            Connection conn = Database.configDB();
            PreparedStatement pst = conn.prepareStatement(query);
            ResultSet rs = pst.executeQuery();

            while (rs.next()) {
                model.addRow(new Object[]{
                        rs.getInt("id_transaksi"),
                        rs.getString("nama_produk"),
                        rs.getTimestamp("tanggal_transaksi"),
                        rs.getDouble("subtotal")
                });
            }

            view.getLaporanPenjualanTable().setModel(model);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
