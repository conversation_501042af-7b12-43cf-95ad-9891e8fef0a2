package model;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import javax.swing.table.DefaultTableModel;

public class LaporanPenjualanModel extends Database {

    public DefaultTableModel getLaporanPenjualanHarian(String tanggal) throws SQLException {
        DefaultTableModel model = new DefaultTableModel();
        model.addColumn("ID Transaksi");
        model.addColumn("Nama Produk");
        model.addColumn("Tanggal Transaksi");
        model.addColumn("Jumlah");
        model.addColumn("Subtotal");

        String sql = "SELECT t.id_transaksi, p.nama_produk, t.tanggal_transaksi, d.jumlah, d.subtotal " +
                     "FROM transaksi t " +
                     "JOIN detail_transaksi d ON t.id_transaksi = d.id_transaksi " +
                     "JOIN produk p ON d.id_produk = p.id_produk " +
                     "WHERE DATE(t.tanggal_transaksi) = ?";

        try (Connection conn = Database.configDB();
             PreparedStatement pst = conn.prepareStatement(sql)) {

            pst.setString(1, tanggal);
            ResultSet rs = pst.executeQuery();

            while (rs.next()) {
                model.addRow(new Object[]{
                    rs.getInt("id_transaksi"),
                    rs.getString("nama_produk"),
                    rs.getDate("tanggal_transaksi"),
                    rs.getInt("jumlah"),
                    rs.getInt("subtotal")
                });
            }
        }

        return model;
    }
}
