/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package view;

import controller.LoginController;
import javax.swing.*;
import model.LoginModel;
import view.AdminDashboard;
import view.KasirDashboard;
import view.PemilikDashboard;

public class Login extends javax.swing.JFrame {

    public Login() {
        initComponents();
        // Tambahkan ActionListener setelah initComponents()
        loginButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnLoginActionPerformed(evt);
            }
        });

    }

    private void btnLoginActionPerformed(java.awt.event.ActionEvent evt) {
        String username = loginNamaField.getText();
        String password = loginPasswordField.getText();

        LoginController loginCtrl = new LoginController();
        LoginModel user = loginCtrl.login(username, password);

        if (user != null) {
            JOptionPane.showMessageDialog(this, "Login berhasil sebagai " + user.getRole());

            switch (user.getRole()) {
                case "admin":
                    new AdminDashboard().setVisible(true);
                    break;
                case "kasir":
                    new KasirDashboard().setVisible(true);
                    break;
                case "pemilik":
                    new PemilikDashboard().setVisible(true);
                    break;
                default:
                    JOptionPane.showMessageDialog(this, "Peran tidak dikenali.");
                    break;
            }

            this.dispose();
        } else {
            JOptionPane.showMessageDialog(this, "Username atau password salah!");
        }
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
private void initComponents() {
    // Panel utama dengan sudut membulat
    JPanel mainPanel = new JPanel() {
        @Override
        protected void paintComponent(java.awt.Graphics g) {
            super.paintComponent(g);
            java.awt.Graphics2D g2 = (java.awt.Graphics2D) g;
            g2.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
            g2.setColor(getBackground());
            g2.fillRoundRect(0, 0, getWidth(), getHeight(), 30, 30);
        }
    };
    mainPanel.setBackground(new java.awt.Color(255, 255, 255));
    mainPanel.setBorder(BorderFactory.createCompoundBorder(
        BorderFactory.createLineBorder(new java.awt.Color(100, 149, 237), 2, true),
        BorderFactory.createEmptyBorder(32, 32, 32, 32)
    ));

    loginLabel = new javax.swing.JLabel();
    usernameLabel = new javax.swing.JLabel();
    passwordLabel = new javax.swing.JLabel();
    loginNamaField = new javax.swing.JTextField();
    loginPasswordField = new javax.swing.JPasswordField();
    loginButton = new javax.swing.JButton();

    setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);
    setTitle("Login UMKM DIMSRY");

    loginLabel.setFont(new java.awt.Font("Segoe UI", 1, 26));
    loginLabel.setForeground(new java.awt.Color(100, 149, 237));
    loginLabel.setHorizontalAlignment(javax.swing.SwingConstants.CENTER);
    loginLabel.setText("🔒 Selamat Datang");
    loginLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 16, 0));

    usernameLabel.setFont(new java.awt.Font("Segoe UI", 0, 15));
    usernameLabel.setText("👤 Username");

    passwordLabel.setFont(new java.awt.Font("Segoe UI", 0, 15));
    passwordLabel.setText("🔒 Password");

    loginNamaField.setFont(new java.awt.Font("Segoe UI", 0, 15));
    loginNamaField.setMargin(new java.awt.Insets(4, 8, 4, 8));
    loginNamaField.setBorder(BorderFactory.createLineBorder(new java.awt.Color(200, 200, 200), 1, true));
    loginNamaField.setToolTipText("Masukkan username");

    loginPasswordField.setFont(new java.awt.Font("Segoe UI", 0, 15));
    loginPasswordField.setMargin(new java.awt.Insets(4, 8, 4, 8));
    loginPasswordField.setBorder(BorderFactory.createLineBorder(new java.awt.Color(200, 200, 200), 1, true));
    loginPasswordField.setToolTipText("Masukkan password");

    loginButton.setBackground(new java.awt.Color(100, 149, 237));
    loginButton.setForeground(java.awt.Color.WHITE);
    loginButton.setFont(new java.awt.Font("Segoe UI", 1, 16));
    loginButton.setText("Login");
    loginButton.setFocusPainted(false);
    loginButton.setCursor(new java.awt.Cursor(java.awt.Cursor.HAND_CURSOR));
    loginButton.setBorder(BorderFactory.createLineBorder(new java.awt.Color(100, 149, 237), 1, true));

    // Layout
    javax.swing.GroupLayout mainLayout = new javax.swing.GroupLayout(mainPanel);
    mainPanel.setLayout(mainLayout);
    mainLayout.setHorizontalGroup(
        mainLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.CENTER)
            .addComponent(loginLabel, javax.swing.GroupLayout.DEFAULT_SIZE, 340, Short.MAX_VALUE)
            .addGroup(mainLayout.createSequentialGroup()
                .addGroup(mainLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(usernameLabel)
                    .addComponent(passwordLabel))
                .addGap(18, 18, 18)
                .addGroup(mainLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                    .addComponent(loginNamaField, javax.swing.GroupLayout.DEFAULT_SIZE, 180, Short.MAX_VALUE)
                    .addComponent(loginPasswordField)))
            .addGroup(mainLayout.createSequentialGroup()
                .addGap(0, 0, Short.MAX_VALUE)
                .addComponent(loginButton, javax.swing.GroupLayout.PREFERRED_SIZE, 130, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(0, 0, Short.MAX_VALUE))
    );
    mainLayout.setVerticalGroup(
        mainLayout.createSequentialGroup()
            .addComponent(loginLabel, javax.swing.GroupLayout.PREFERRED_SIZE, 48, javax.swing.GroupLayout.PREFERRED_SIZE)
            .addGap(28, 28, 28)
            .addGroup(mainLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                .addComponent(usernameLabel, javax.swing.GroupLayout.PREFERRED_SIZE, 32, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addComponent(loginNamaField, javax.swing.GroupLayout.PREFERRED_SIZE, 36, javax.swing.GroupLayout.PREFERRED_SIZE))
            .addGap(18, 18, 18)
            .addGroup(mainLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                .addComponent(passwordLabel, javax.swing.GroupLayout.PREFERRED_SIZE, 32, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addComponent(loginPasswordField, javax.swing.GroupLayout.PREFERRED_SIZE, 36, javax.swing.GroupLayout.PREFERRED_SIZE))
            .addGap(32, 32, 32)
            .addComponent(loginButton, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE)
            .addGap(10, 10, 10)
    );

    getContentPane().setLayout(new java.awt.GridBagLayout());
    getContentPane().add(mainPanel);

    pack();
    setLocationRelativeTo(null);
}
// </editor-fold>//GEN-END:initComponents

    private void loginNamaFieldActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_loginNamaFieldActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_loginNamaFieldActionPerformed

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        SwingUtilities.invokeLater(() -> new Login().setVisible(true));
    }


    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton loginButton;
    public javax.swing.JLabel loginLabel;
    private javax.swing.JTextField loginNamaField;
    private javax.swing.JPasswordField loginPasswordField;
    private javax.swing.JLabel passwordLabel;
    private javax.swing.JLabel usernameLabel;
    // End of variables declaration//GEN-END:variables
}