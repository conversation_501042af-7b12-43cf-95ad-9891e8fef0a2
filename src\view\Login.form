<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace min="-2" pref="174" max="-2" attributes="0"/>
              <Component id="loginLabel" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="76" max="-2" attributes="0"/>
          </Group>
          <Group type="102" attributes="0">
              <EmptySpace min="-2" pref="29" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="usernameLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="passwordLabel" alignment="0" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace pref="89" max="32767" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="loginButton" min="-2" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" max="-2" attributes="0">
                      <Component id="loginPasswordField" pref="170" max="32767" attributes="0"/>
                      <Component id="loginNamaField" max="32767" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace min="0" pref="84" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="loginLabel" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="34" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="usernameLabel" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="loginNamaField" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="31" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="passwordLabel" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="loginPasswordField" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="80" max="-2" attributes="0"/>
              <Component id="loginButton" min="-2" max="-2" attributes="0"/>
              <EmptySpace pref="155" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="loginLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Login "/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="usernameLabel">
      <Properties>
        <Property name="text" type="java.lang.String" value="Username"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="passwordLabel">
      <Properties>
        <Property name="text" type="java.lang.String" value="Password"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="loginNamaField">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="loginNamaFieldActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JTextField" name="loginPasswordField">
    </Component>
    <Component class="javax.swing.JButton" name="loginButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Login"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
