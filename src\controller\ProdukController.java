package controller;
import model.Database;
import model.Produk;
import view.PengelolaanProduk;
import java.awt.event.*;
import java.sql.*;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;

public class ProdukController implements ActionListener, MouseListener {

    private Produk data;
    private PengelolaanProduk frm;

    public ProdukController(Produk data, PengelolaanProduk frm) {
        this.data = data;
        this.frm = frm;

        this.frm.tambahButton.addActionListener(this);
        this.frm.simpanButton.addActionListener(this);
        this.frm.editButton.addActionListener(this);
        this.frm.hapusButton.addActionListener(this);
        this.frm.pengelolaanProduk.addMouseListener(this); 

        tampilDataProduk();
    }

    public void kosongkanForm() {
        frm.idField.setEditable(true);
        frm.idField.setText("");
        frm.namaField.setText("");
        frm.hargaField.setText("");
        frm.deskripsiField.setText("");
        frm.stokField.setText("");
    }

    public void tampilDataProduk() {
        DefaultTableModel model = new DefaultTableModel();
        model.addColumn("No");
        model.addColumn("ID Produk");
        model.addColumn("Nama Produk");
        model.addColumn("Harga");
        model.addColumn("Deskripsi");
        model.addColumn("Stok");

        try (Connection conn = Database.configDB();
             Statement stm = conn.createStatement();
             ResultSet res = stm.executeQuery("SELECT * FROM produk")) {

            int no = 1;
            while (res.next()) {
                model.addRow(new Object[]{
                    no++,
                    res.getString("id_produk"),
                    res.getString("nama_produk"),
                    res.getInt("harga"),
                    res.getString("deskripsi"),
                    res.getInt("stok")
                });
            }

            frm.pengelolaanProduk.setModel(model);

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(null, "Gagal menampilkan data: " + e.getMessage());
        }
    }

    @Override
    public void actionPerformed(ActionEvent ae) {
        if (ae.getSource() == frm.tambahButton) {
            kosongkanForm();
        } 
        else if (ae.getSource() == frm.simpanButton) {
            if (!isiFormKeModel()) return;

            try {
                if (data.simpanProduk(data)) {
                    JOptionPane.showMessageDialog(null, "Data berhasil disimpan");
                    kosongkanForm();
                    tampilDataProduk();
                }
            } catch (SQLException e) {
                JOptionPane.showMessageDialog(null, "Gagal menyimpan data: " + e.getMessage());
            }

        } 
        else if (ae.getSource() == frm.editButton) {
            if (!isiFormKeModel()) return;

            try {
                if (data.updateProduk(data)) {
                    JOptionPane.showMessageDialog(null, "Data berhasil diubah");
                    kosongkanForm();
                    tampilDataProduk();
                }
            } catch (SQLException e) {
                JOptionPane.showMessageDialog(null, "Gagal mengedit data: " + e.getMessage());
            }

        } 
        else if (ae.getSource() == frm.hapusButton) {
            data.setIdProduk(frm.idField.getText());

            try {
                if (data.hapusProduk(data)) {
                    JOptionPane.showMessageDialog(null, "Data berhasil dihapus");
                    kosongkanForm();
                    tampilDataProduk();
                }
            } catch (SQLException e) {
                JOptionPane.showMessageDialog(null, "Gagal menghapus data: " + e.getMessage());
            }
        }
    }

    private boolean isiFormKeModel() {
        data.setIdProduk(frm.idField.getText());
        data.setNamaProduk(frm.namaField.getText());
        data.setDeskripsi(frm.deskripsiField.getText());

        try {
            int harga = Integer.parseInt(frm.hargaField.getText().replaceAll("[^0-9]", ""));
            int stok = Integer.parseInt(frm.stokField.getText().replaceAll("[^0-9]", ""));
            data.setHarga(harga);
            data.setStok(stok);
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "Harga dan Stok harus berupa angka!");
            return false;
        }
        return true;
    }

    @Override
    public void mouseClicked(MouseEvent me) {
        int baris = frm.pengelolaanProduk.rowAtPoint(me.getPoint());

        frm.idField.setEditable(false);
        frm.idField.setText(frm.pengelolaanProduk.getValueAt(baris, 1).toString());
        frm.namaField.setText(frm.pengelolaanProduk.getValueAt(baris, 2).toString());
        frm.hargaField.setText(frm.pengelolaanProduk.getValueAt(baris, 3).toString());
        frm.deskripsiField.setText(frm.pengelolaanProduk.getValueAt(baris, 4).toString());
        frm.stokField.setText(frm.pengelolaanProduk.getValueAt(baris, 5).toString());
    }

    @Override public void mousePressed(MouseEvent e) {}
    @Override public void mouseReleased(MouseEvent e) {}
    @Override public void mouseEntered(MouseEvent e) {}
    @Override public void mouseExited(MouseEvent e) {}
}
