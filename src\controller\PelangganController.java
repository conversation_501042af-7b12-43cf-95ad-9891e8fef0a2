package controller;
import model.Database;
import model.Pelanggan;
import view.DataPelanggan;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.event.*;
import java.sql.*;

public class PelangganController implements ActionListener, MouseListener {

    private Pelanggan data;
    private DataPelanggan frm;

    public PelangganController(Pelanggan data, DataPelanggan frm) {
        this.data = data;
        this.frm = frm;

        this.frm.tambahButton.addActionListener(this);
        this.frm.simpanButton.addActionListener(this);
        this.frm.editButton.addActionListener(this);
        this.frm.hapusButton.addActionListener(this);
        this.frm.dataPelangganTabel.addMouseListener(this);

        TampilDataPlgn();
    }

    public void KosongFormPlgn() {
        frm.idField.setEditable(true);
        frm.idField.setText("");
        frm.namaField.setText("");
        frm.noHpField.setText("");
        frm.alamatField.setText("");
        frm.memberComboBox.setSelectedIndex(0); // default ke index pertama
    }

    public void TampilDataPlgn() {
        DefaultTableModel model = new DefaultTableModel();
        model.addColumn("No");
        model.addColumn("ID Pelanggan");
        model.addColumn("Nama Pelanggan");
        model.addColumn("No HP");
        model.addColumn("Alamat");
        model.addColumn("Status Member");

        try {
            Connection conn = Database.configDB();
            Statement stmt = conn.createStatement();
            ResultSet res = stmt.executeQuery("SELECT * FROM pelanggan");
            int no = 1;

            while (res.next()) {
                model.addRow(new Object[]{
                    no++,
                    res.getString("id_pelanggan"),
                    res.getString("nama_pelanggan"),
                    res.getString("no_hp"),
                    res.getString("alamat"),
                    res.getString("status_member")
                });
            }
            frm.dataPelangganTabel.setModel(model);
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(null, "Gagal menampilkan data: " + e.getMessage());
        }
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        if (e.getSource() == frm.tambahButton) {
            KosongFormPlgn();
        } else if (e.getSource() == frm.simpanButton) {
            data.setId_pelanggan(frm.idField.getText());
            data.setNama_pelanggan(frm.namaField.getText());
            data.setNo_hp(frm.noHpField.getText());
            data.setAlamat(frm.alamatField.getText());
            data.setStatus_member(frm.memberComboBox.getSelectedItem().toString());

            try {
                if (data.SimpanPelanggan(data)) {
                    JOptionPane.showMessageDialog(null, "Data berhasil disimpan.");
                    KosongFormPlgn();
                    TampilDataPlgn();
                }
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(null, "Gagal menyimpan data: " + ex.getMessage());
            }

        } else if (e.getSource() == frm.editButton) {
            data.setId_pelanggan(frm.idField.getText());
            data.setNama_pelanggan(frm.namaField.getText());
            data.setNo_hp(frm.noHpField.getText());
            data.setAlamat(frm.alamatField.getText());
            data.setStatus_member(frm.memberComboBox.getSelectedItem().toString());

            try {
                if (data.UpdatePelanggan(data)) {
                    JOptionPane.showMessageDialog(null, "Data berhasil diupdate.");
                    KosongFormPlgn();
                    TampilDataPlgn();
                }
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(null, "Gagal mengupdate data: " + ex.getMessage());
            }

        } else if (e.getSource() == frm.hapusButton) {
            data.setId_pelanggan(frm.idField.getText());

            try {
                if (data.HapusPelanggan(data)) {
                    JOptionPane.showMessageDialog(null, "Data berhasil dihapus.");
                    KosongFormPlgn();
                    TampilDataPlgn();
                }
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(null, "Gagal menghapus data: " + ex.getMessage());
            }
        }
    }

    @Override
    public void mouseClicked(MouseEvent me) {
        int row = frm.dataPelangganTabel.getSelectedRow();
        frm.idField.setEditable(false);
        frm.idField.setText(frm.dataPelangganTabel.getValueAt(row, 1).toString());
        frm.namaField.setText(frm.dataPelangganTabel.getValueAt(row, 2).toString());
        frm.noHpField.setText(frm.dataPelangganTabel.getValueAt(row, 3).toString());
        frm.alamatField.setText(frm.dataPelangganTabel.getValueAt(row, 4).toString());
        frm.memberComboBox.setSelectedItem(frm.dataPelangganTabel.getValueAt(row, 5).toString());
    }

    @Override public void mousePressed(MouseEvent e) {}
    @Override public void mouseReleased(MouseEvent e) {}
    @Override public void mouseEntered(MouseEvent e) {}
    @Override public void mouseExited(MouseEvent e) {}
}
