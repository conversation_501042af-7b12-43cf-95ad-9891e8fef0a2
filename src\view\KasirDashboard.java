package view;

import javax.swing.*;
import umkm_dimsry.*;

public class KasirDashboard extends J<PERSON>rame {
    public KasirDashboard() {
        setTitle("Dashboard Kasir");
        setSize(400, 300);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        String[] fitur = {
            "Input Transaksi Penjualan",
            "Cetak Nota",
            "Login"
        };

        String selected = (String) JOptionPane.showInputDialog(this, "Pilih fitur:", "Menu Kasir",
                JOptionPane.QUESTION_MESSAGE, null, fitur, fitur[0]);

        if (selected != null) {
            dispose(); // tutup dashboard
            switch (selected) {
                case "Input Transaksi Penjualan":
                    TransaksiMain.main(null); break;
                case "Login":
                    Login.main(null); break;
                default:
                    JOptionPane.showMessageDialog(null, "Fitur belum tersedia.");
            }
        }
    }
}
