package model;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class Transaksi extends Database {

    private String nama;
    private String id_produk;
    private int jumlah;
    private int harga_satuan;
    private int subtotal;

    // Getter dan Setter
    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getId_produk() {
        return id_produk;
    }

    public void setId_produk(String id_produk) {
        this.id_produk = id_produk;
    }

    public int getJumlah() {
        return jumlah;
    }

    public void setJumlah(int jumlah) {
        this.jumlah = jumlah;
    }

    public int getHarga_satuan() {
        return harga_satuan;
    }

    public void setHarga_satuan(int harga_satuan) {
        this.harga_satuan = harga_satuan;
    }

    public int getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(int subtotal) {
        this.subtotal = subtotal;
    }

    // Simpan transaksi utama + detail + update stok
    public boolean simpanTransaksi(Transaksi data) throws SQLException {
        Connection conn = Database.configDB();
        conn.setAutoCommit(false);

        try (
                PreparedStatement pst1 = conn.prepareStatement(
                        "INSERT INTO transaksi (nama_pembeli) VALUES (?)",
                        PreparedStatement.RETURN_GENERATED_KEYS)) {
            pst1.setString(1, data.getNama());
            pst1.executeUpdate();

            ResultSet rs = pst1.getGeneratedKeys();
            int idTransaksi = 0;
            if (rs.next()) {
                idTransaksi = rs.getInt(1);
            } else {
                throw new SQLException("Gagal mengambil ID transaksi baru.");
            }

            // Simpan ke detail_transaksi
            try (PreparedStatement pst2 = conn.prepareStatement(
                    "INSERT INTO detail_transaksi (id_transaksi, id_produk, jumlah, harga_satuan, subtotal) VALUES (?, ?, ?, ?, ?)")) {
                pst2.setInt(1, idTransaksi);
                pst2.setString(2, data.getId_produk());
                pst2.setInt(3, data.getJumlah());
                pst2.setInt(4, data.getHarga_satuan());
                pst2.setInt(5, data.getSubtotal());
                pst2.executeUpdate();
            }

            // Kurangi stok produk
            try (PreparedStatement pst3 = conn.prepareStatement(
                    "UPDATE produk SET stok = stok - ? WHERE id_produk = ?")) {
                pst3.setInt(1, data.getJumlah());
                pst3.setString(2, data.getId_produk());
                pst3.executeUpdate();
            }

            conn.commit();
            return true;

        } catch (SQLException e) {
            conn.rollback();
            System.err.println("Transaksi gagal: " + e.getMessage());
            return false;
        } finally {
            conn.setAutoCommit(true);
            conn.close();
        }
    }

    // Ambil status member dari tabel pelanggan
    public String getStatusMemberByNama(String nama) throws SQLException {
        String sql = "SELECT status_member FROM pelanggan WHERE nama_pelanggan = ?";
        try (
                Connection conn = Database.configDB();
                PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, nama);
            ResultSet rs = pstmt.executeQuery();
            if (rs.next()) {
                return rs.getString("status_member");
            } else {
                return "non-member"; // default jika tidak ditemukan
            }
        }
    }
}
