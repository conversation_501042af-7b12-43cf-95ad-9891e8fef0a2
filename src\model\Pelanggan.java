package model;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class Pelanggan extends Database {
    private String id_pelanggan;
    private String nama_pelanggan;
    private String no_hp;
    private String alamat;
    private String status_member;

    // Getter & Setter
    public String getId_pelanggan() { return id_pelanggan; }
    public void setId_pelanggan(String id_pelanggan) { this.id_pelanggan = id_pelanggan; }

    public String getNama_pelanggan() { return nama_pelanggan; }
    public void setNama_pelanggan(String nama_pelanggan) { this.nama_pelanggan = nama_pelanggan; }

    public String getNo_hp() { return no_hp; }
    public void setNo_hp(String no_hp) { this.no_hp = no_hp; }

    public String getAlamat() { return alamat; }
    public void setAlamat(String alamat) { this.alamat = alamat; }

    public String getStatus_member() { return status_member; }
    public void setStatus_member(String status_member) { this.status_member = status_member; }

    // Simpan
    public boolean SimpanPelanggan(Pelanggan data) throws SQLException {
        String sql = "INSERT INTO pelanggan (id_pelanggan, nama_pelanggan, no_hp, alamat, status_member) VALUES (?, ?, ?, ?, ?)";
        try (Connection conn = Database.configDB(); PreparedStatement pst = conn.prepareStatement(sql)) {
            pst.setString(1, data.getId_pelanggan());
            pst.setString(2, data.getNama_pelanggan());
            pst.setString(3, data.getNo_hp());
            pst.setString(4, data.getAlamat());
            pst.setString(5, data.getStatus_member());
            pst.executeUpdate();
            return true;
        }
    }

    // Update
    public boolean UpdatePelanggan(Pelanggan data) throws SQLException {
        String sql = "UPDATE pelanggan SET nama_pelanggan=?, no_hp=?, alamat=?, status_member=? WHERE id_pelanggan=?";
        try (Connection conn = Database.configDB(); PreparedStatement pst = conn.prepareStatement(sql)) {
            pst.setString(1, data.getNama_pelanggan());
            pst.setString(2, data.getNo_hp());
            pst.setString(3, data.getAlamat());
            pst.setString(4, data.getStatus_member());
            pst.setString(5, data.getId_pelanggan());
            pst.executeUpdate();
            return true;
        }
    }

    // Hapus
    public boolean HapusPelanggan(Pelanggan data) throws SQLException {
        String sql = "DELETE FROM pelanggan WHERE id_pelanggan=?";
        try (Connection conn = Database.configDB(); PreparedStatement pst = conn.prepareStatement(sql)) {
            pst.setString(1, data.getId_pelanggan());
            pst.executeUpdate();
            return true;
        }
    }
}
