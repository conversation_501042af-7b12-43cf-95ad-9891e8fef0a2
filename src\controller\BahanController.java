package controller;
import model.Database;
import model.Bahan;
import view.PengelolaanStokBahanBaku;
import java.sql.SQLException;
import java.awt.event.*;
import java.sql.*;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;

public class <PERSON>hanController implements ActionListener, MouseListener {

    private Bahan data;
    private PengelolaanStokBahanBaku frm;

    public BahanController(Bahan data, PengelolaanStokBahanBaku frm) {
        this.data = data;
        this.frm = frm;

        this.frm.tambahButton.addActionListener(this);
        this.frm.simpanButton.addActionListener(this);
        this.frm.editButton.addActionListener(this);
        this.frm.hapusButton.addActionListener(this);
        this.frm.pengelolaanStokTabel.addMouseListener(this);

        TampilDataBahan();
    }

    public void KosongFormBahan() {
        frm.idField.setEditable(true);
        frm.idField.setText("");
        frm.namaField.setText("");
        frm.stokField.setText("");
        frm.satuanField.setText("");
    }

    public void TampilDataBahan() {
        DefaultTableModel model = new DefaultTableModel();
        model.addColumn("No");
        model.addColumn("ID Bahan");
        model.addColumn("Nama Bahan");
        model.addColumn("Stok");
        model.addColumn("Satuan");

        try {
            int no = 1;
            String sql = "SELECT * FROM bahan_baku";
            Connection conn = Database.configDB();
            Statement stm = conn.createStatement();
            ResultSet res = stm.executeQuery(sql);

            while (res.next()) {
                model.addRow(new Object[]{
                    no++,
                    res.getString("id_bahan"),
                    res.getString("nama_bahan"),
                    res.getString("stok"),
                    res.getString("satuan")
                });
            }

            frm.pengelolaanStokTabel.setModel(model);
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(null, "Error tampil: " + e.getMessage());
        }
    }

    @Override
    public void actionPerformed(ActionEvent ae) {
        if (ae.getSource() == frm.tambahButton) {
            KosongFormBahan();

        } else if (ae.getSource() == frm.simpanButton) {
    data.setIdBahan(frm.idField.getText());
    data.setNamaBahan(frm.namaField.getText());

    // Validasi stok
    String stokStr = frm.stokField.getText().replaceAll("[^0-9]", "");
    int stok = 0;
    try {
        stok = Integer.parseInt(stokStr);
    } catch (NumberFormatException ex) {
        JOptionPane.showMessageDialog(null, "Stok harus berupa angka!");
        return;
    }
    data.setStok(stok);

    data.setSatuan(frm.satuanField.getText());

    try {
        if (data.simpanBahan(data)) {
            JOptionPane.showMessageDialog(null, "Simpan Data Berhasil");
            KosongFormBahan();
            TampilDataBahan();
        }
    } catch (SQLException ex) {
        JOptionPane.showMessageDialog(null, "Error simpan: " + ex.getMessage());
    }

    

        } else if (ae.getSource() == frm.editButton) {
            data.setIdBahan(frm.idField.getText());
            data.setNamaBahan(frm.namaField.getText());
            data.setStok(Integer.parseInt(frm.stokField.getText()));
            data.setSatuan(frm.satuanField.getText());

            try {
                if (data.updateBahan(data)) {
                    JOptionPane.showMessageDialog(null, "Update Data Berhasil");
                    KosongFormBahan();
                    TampilDataBahan();
                }
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(null, "Error update: " + ex.getMessage());
            }

        } else if (ae.getSource() == frm.hapusButton) {
            data.setIdBahan(frm.idField.getText());

            try {
                if (data.hapusBahan(data)) {
                    JOptionPane.showMessageDialog(null, "Hapus Data Berhasil");
                    KosongFormBahan();
                    TampilDataBahan();
                }
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(null, "Error hapus: " + ex.getMessage());
            }
        }
    }

    @Override
    public void mouseClicked(MouseEvent me) {
        int baris = frm.pengelolaanStokTabel.rowAtPoint(me.getPoint());

        frm.idField.setEditable(false);
        frm.idField.setText(frm.pengelolaanStokTabel.getValueAt(baris, 1).toString());
        frm.namaField.setText(frm.pengelolaanStokTabel.getValueAt(baris, 2).toString());
        frm.stokField.setText(frm.pengelolaanStokTabel.getValueAt(baris, 3).toString());
        frm.satuanField.setText(frm.pengelolaanStokTabel.getValueAt(baris, 4).toString());
    }

    @Override
    public void mousePressed(MouseEvent e) {}
    @Override
    public void mouseReleased(MouseEvent e) {}
    @Override
    public void mouseEntered(MouseEvent e) {}
    @Override
    public void mouseExited(MouseEvent e) {}
}
