<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.5" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <Component id="tambahButton" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="68" max="-2" attributes="0"/>
                      <Component id="simpanButton" min="-2" max="-2" attributes="0"/>
                      <EmptySpace pref="86" max="32767" attributes="0"/>
                      <Component id="editButton" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="80" max="-2" attributes="0"/>
                      <Component id="hapusButton" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="31" max="-2" attributes="0"/>
                  </Group>
                  <Component id="jScrollPane1" min="-2" pref="574" max="-2" attributes="0"/>
              </Group>
          </Group>
          <Group type="102" attributes="0">
              <EmptySpace min="-2" pref="210" max="-2" attributes="0"/>
              <Component id="dataPelanggan" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace max="32767" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="alamatLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="noHpLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="namaLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="idLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="memberLabel" alignment="0" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="94" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="1" attributes="0">
                  <Group type="103" alignment="1" groupAlignment="0" max="-2" attributes="0">
                      <Component id="alamatField" max="32767" attributes="0"/>
                      <Component id="noHpField" alignment="0" max="32767" attributes="0"/>
                      <Component id="idField" alignment="0" min="-2" pref="159" max="-2" attributes="0"/>
                  </Group>
                  <Component id="namaField" min="-2" pref="159" max="-2" attributes="0"/>
                  <Component id="memberComboBox" alignment="0" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="119" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="16" max="-2" attributes="0"/>
              <Component id="dataPelanggan" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="53" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="idLabel" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="idField" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="unrelated" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="namaField" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="namaLabel" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="noHpField" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="noHpLabel" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="16" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="alamatField" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="alamatLabel" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="memberLabel" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="memberComboBox" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="48" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="1" attributes="0">
                  <Component id="hapusButton" min="-2" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="tambahButton" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="simpanButton" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="editButton" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="182" max="-2" attributes="0"/>
              <EmptySpace pref="33" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="dataPelanggan">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="24" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Data Pelanggan"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="dataPelangganTabel">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Times New Roman" size="14" style="2"/>
            </Property>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="4" rowCount="4">
                <Column editable="true" title="ID Pelanggan" type="java.lang.Object"/>
                <Column editable="true" title="Nama Pelanggan" type="java.lang.Object"/>
                <Column editable="true" title="No HP" type="java.lang.Object"/>
                <Column editable="true" title="Alamat" type="java.lang.Object"/>
              </Table>
            </Property>
            <Property name="columnModel" type="javax.swing.table.TableColumnModel" editor="org.netbeans.modules.form.editors2.TableColumnModelEditor">
              <TableColumnModel selectionModel="0">
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
              </TableColumnModel>
            </Property>
            <Property name="tableHeader" type="javax.swing.table.JTableHeader" editor="org.netbeans.modules.form.editors2.JTableHeaderEditor">
              <TableHeader reorderingAllowed="true" resizingAllowed="true"/>
            </Property>
          </Properties>
          <AuxValues>
            <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
          </AuxValues>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="namaLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Nama Pelanggan"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="noHpLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="No HP"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="alamatLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Alamat"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="namaField">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="namaFieldActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="noHpField">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="noHpFieldActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="alamatField">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="alamatFieldActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="tambahButton">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="18" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Tambah"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="hapusButton">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="18" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Hapus"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="hapusButtonActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="simpanButton">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="18" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Simpan"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="simpanButtonActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="editButton">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="18" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Edit"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="idLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="ID Pelanggan"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="idField">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="idFieldActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="memberLabel">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Member"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JComboBox" name="memberComboBox">
      <Properties>
        <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
          <StringArray count="2">
            <StringItem index="0" value="member"/>
            <StringItem index="1" value="non-member"/>
          </StringArray>
        </Property>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
  </SubComponents>
</Form>
