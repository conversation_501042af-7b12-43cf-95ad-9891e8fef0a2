<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jPanel1" max="32767" attributes="0"/>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jLabel2" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel1" alignment="0" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="37" max="-2" attributes="0"/>
              <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
          <Component id="jPanel1" alignment="1" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Component id="pengelolaanProdukButton" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="pengelolaanStokButton" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="laporanPenjualanButton" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="dashboardButton" alignment="0" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace pref="17" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="63" max="-2" attributes="0"/>
                  <Component id="pengelolaanProdukButton" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
                  <Component id="pengelolaanStokButton" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
                  <Component id="laporanPenjualanButton" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="27" max="-2" attributes="0"/>
                  <Component id="dashboardButton" min="-2" max="-2" attributes="0"/>
                  <EmptySpace pref="161" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JButton" name="pengelolaanProdukButton">
          <Properties>
            <Property name="text" type="java.lang.String" value="Pengelolaan Produk"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="pengelolaanProdukButtonActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="pengelolaanStokButton">
          <Properties>
            <Property name="text" type="java.lang.String" value="Pengelolaan Bahan"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="pengelolaanStokButtonActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="laporanPenjualanButton">
          <Properties>
            <Property name="text" type="java.lang.String" value="Laporan Penjualan"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="laporanPenjualanButtonActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="dashboardButton">
          <Properties>
            <Property name="text" type="java.lang.String" value="Dashboard"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="dashboardButtonActionPerformed"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="text" type="java.lang.String" value="Halo Admin, Selamat Datang di Dashboard UMKM Dimsry!"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="Terima kasih sudah terus semangat membangun UMKM!"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="Yuk, cek data produk, pantau transaksi, dan kelola usahamu dengan mudah. "/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
