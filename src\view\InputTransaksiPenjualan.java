/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package view;

/**
 *
 * <AUTHOR>
 */
public class InputTransaksiPenjualan extends javax.swing.JFrame {

    /**
     * Creates new form InputTransaksiPenjualan
     */
    public InputTransaksiPenjualan() {
        initComponents();

        // Tambahan tampilan modern
        getContentPane().setBackground(new java.awt.Color(245, 245, 255));
        InputTransaksiText.setFont(new java.awt.Font("Segoe UI", 1, 22));
        InputTransaksiText.setForeground(new java.awt.Color(100, 149, 237));

        // Field padding
        namaProdukField.setBorder(javax.swing.BorderFactory.createCompoundBorder(
            namaProdukField.getBorder(),
            javax.swing.BorderFactory.createEmptyBorder(6, 8, 6, 8)
        ));
        produkField.setBorder(javax.swing.BorderFactory.createCompoundBorder(
            produkField.getBorder(),
            javax.swing.BorderFactory.createEmptyBorder(6, 8, 6, 8)
        ));

        // Tombol rounded dan warna
        for (javax.swing.JButton btn : new javax.swing.JButton[]{tambahButton, selesaiButton, simpanButton}) {
            btn.setBackground(new java.awt.Color(100, 149, 237));
            btn.setForeground(java.awt.Color.WHITE);
            btn.setFont(new java.awt.Font("Segoe UI", java.awt.Font.BOLD, 15));
            btn.setFocusPainted(false);
            btn.setBorder(javax.swing.BorderFactory.createLineBorder(new java.awt.Color(100, 149, 237), 1, true));
            btn.setCursor(new java.awt.Cursor(java.awt.Cursor.HAND_CURSOR));
        }
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        memberBtnGrp = new javax.swing.ButtonGroup();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        InputTransaksiText = new javax.swing.JLabel();
        memberText = new javax.swing.JLabel();
        memberRadio = new javax.swing.JRadioButton();
        nonMemberRadio = new javax.swing.JRadioButton();
        namaLabel = new javax.swing.JLabel();
        produkDinsumLabel = new javax.swing.JLabel();
        jumlahProdukLabel = new javax.swing.JLabel();
        namaProdukField = new javax.swing.JTextField();
        produkField = new javax.swing.JTextField();
        totalHargaField = new javax.swing.JLabel();
        simpanButton = new javax.swing.JButton();
        tambahButton = new javax.swing.JButton();
        selesaiButton = new javax.swing.JButton();
        jScrollPane2 = new javax.swing.JScrollPane();
        keranjangTabel = new javax.swing.JTable();
        kerangScrollPane = new javax.swing.JScrollPane();

        jTable1.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null}
            },
            new String [] {
                "Title 1", "Title 2", "Title 3", "Title 4"
            }
        ));
        jScrollPane1.setViewportView(jTable1);

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        InputTransaksiText.setFont(new java.awt.Font("Tahoma", 1, 18)); // NOI18N
        InputTransaksiText.setText("Input Transaksi Penjualanan");

        memberText.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        memberText.setText("Member / non - Member");

        memberRadio.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        memberRadio.setText("Member");
        memberRadio.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                memberRadioActionPerformed(evt);
            }
        });

        nonMemberRadio.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        nonMemberRadio.setText("Non-Member");
        nonMemberRadio.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                nonMemberRadioActionPerformed(evt);
            }
        });

        namaLabel.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        namaLabel.setText("Nama");

        produkDinsumLabel.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        produkDinsumLabel.setText("Produk Dinsum");

        jumlahProdukLabel.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        jumlahProdukLabel.setText("Jumlah Produk");

        namaProdukField.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                namaProdukFieldActionPerformed(evt);
            }
        });

        produkField.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                produkFieldActionPerformed(evt);
            }
        });

        totalHargaField.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        totalHargaField.setText("Total Harga");

        simpanButton.setFont(new java.awt.Font("Tahoma", 1, 14)); // NOI18N
        simpanButton.setText("Simpan");

        tambahButton.setFont(new java.awt.Font("Tahoma", 1, 14)); // NOI18N
        tambahButton.setText("Tambah");

        selesaiButton.setFont(new java.awt.Font("Tahoma", 1, 14)); // NOI18N
        selesaiButton.setText("Selesai");

        keranjangTabel.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null}
            },
            new String [] {
                "Title 1", "Title 2", "Title 3", "Title 4"
            }
        ));
        jScrollPane2.setViewportView(keranjangTabel);

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGap(36, 36, 36)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(memberText)
                                    .addComponent(namaLabel)
                                    .addComponent(produkDinsumLabel)
                                    .addComponent(jumlahProdukLabel)
                                    .addComponent(tambahButton))
                                .addGap(34, 34, 34)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(selesaiButton)
                                    .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                                        .addComponent(totalHargaField)
                                        .addComponent(nonMemberRadio)
                                        .addComponent(memberRadio)
                                        .addComponent(produkField, javax.swing.GroupLayout.DEFAULT_SIZE, 162, Short.MAX_VALUE)
                                        .addComponent(namaProdukField))))
                            .addComponent(jScrollPane2, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                            .addComponent(kerangScrollPane, javax.swing.GroupLayout.PREFERRED_SIZE, 100, javax.swing.GroupLayout.PREFERRED_SIZE)))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(148, 148, 148)
                        .addComponent(InputTransaksiText))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(212, 212, 212)
                        .addComponent(simpanButton)))
                .addContainerGap(36, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addComponent(InputTransaksiText)
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(memberText)
                    .addComponent(memberRadio))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                .addComponent(nonMemberRadio)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(namaLabel)
                    .addComponent(namaProdukField, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(produkDinsumLabel, javax.swing.GroupLayout.PREFERRED_SIZE, 16, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(produkField, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jumlahProdukLabel)
                    .addComponent(totalHargaField))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(selesaiButton, javax.swing.GroupLayout.Alignment.TRAILING)
                    .addComponent(tambahButton))
                .addGap(18, 18, 18)
                .addComponent(jScrollPane2, javax.swing.GroupLayout.PREFERRED_SIZE, 129, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(34, 34, 34)
                .addComponent(kerangScrollPane, javax.swing.GroupLayout.PREFERRED_SIZE, 100, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, 14, Short.MAX_VALUE)
                .addComponent(simpanButton)
                .addContainerGap())
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void memberRadioActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_memberRadioActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_memberRadioActionPerformed

    private void nonMemberRadioActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_nonMemberRadioActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_nonMemberRadioActionPerformed

    private void namaProdukFieldActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_namaProdukFieldActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_namaProdukFieldActionPerformed

    private void produkFieldActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_produkFieldActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_produkFieldActionPerformed

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(InputTransaksiPenjualan.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(InputTransaksiPenjualan.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(InputTransaksiPenjualan.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(InputTransaksiPenjualan.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new InputTransaksiPenjualan().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    public javax.swing.JLabel InputTransaksiText;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JScrollPane jScrollPane2;
    private javax.swing.JTable jTable1;
    public javax.swing.JLabel jumlahProdukLabel;
    public javax.swing.JScrollPane kerangScrollPane;
    public javax.swing.JTable keranjangTabel;
    public javax.swing.ButtonGroup memberBtnGrp;
    public javax.swing.JRadioButton memberRadio;
    public javax.swing.JLabel memberText;
    public javax.swing.JLabel namaLabel;
    public javax.swing.JTextField namaProdukField;
    public javax.swing.JRadioButton nonMemberRadio;
    public javax.swing.JLabel produkDinsumLabel;
    public javax.swing.JTextField produkField;
    public javax.swing.JButton selesaiButton;
    public javax.swing.JButton simpanButton;
    public javax.swing.JButton tambahButton;
    public javax.swing.JLabel totalHargaField;
    // End of variables declaration//GEN-END:variables
}
