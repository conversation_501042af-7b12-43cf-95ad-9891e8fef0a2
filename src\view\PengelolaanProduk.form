<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.5" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="deskripsiLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="hargaLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="stokLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="namaLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="tambahButton" min="-2" max="-2" attributes="0"/>
                              <EmptySpace min="-2" pref="33" max="-2" attributes="0"/>
                              <Component id="simpanButton" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Component id="idLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
                              <Component id="editButton" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="32767" attributes="0"/>
                              <Component id="hapusButton" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Component id="namaField" max="32767" attributes="0"/>
                          <Component id="deskripsiField" alignment="1" max="32767" attributes="0"/>
                          <Component id="stokField" alignment="1" max="32767" attributes="0"/>
                          <Component id="hargaField" alignment="1" max="32767" attributes="0"/>
                          <Component id="idField" alignment="0" max="32767" attributes="0"/>
                      </Group>
                  </Group>
                  <Group type="102" attributes="0">
                      <Component id="jScrollPane1" min="-2" pref="434" max="-2" attributes="0"/>
                      <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace max="32767" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="125" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="35" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="idLabel" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="idField" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="namaLabel" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="namaField" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="hargaLabel" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="hargaField" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="16" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="deskripsiField" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="deskripsiLabel" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="stokField" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="stokLabel" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="tambahButton" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="simpanButton" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="editButton" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="hapusButton" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Component id="jScrollPane1" pref="195" max="32767" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Pengelolaan Produk Dimsum"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="pengelolaanProduk">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="4" rowCount="4">
                <Column editable="true" title="Nama Produk" type="java.lang.Object"/>
                <Column editable="true" title="Harga" type="java.lang.Object"/>
                <Column editable="true" title="Deskripsi" type="java.lang.Object"/>
                <Column editable="true" title="Stok" type="java.lang.Object"/>
              </Table>
            </Property>
            <Property name="columnModel" type="javax.swing.table.TableColumnModel" editor="org.netbeans.modules.form.editors2.TableColumnModelEditor">
              <TableColumnModel selectionModel="0">
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
              </TableColumnModel>
            </Property>
            <Property name="tableHeader" type="javax.swing.table.JTableHeader" editor="org.netbeans.modules.form.editors2.JTableHeaderEditor">
              <TableHeader reorderingAllowed="true" resizingAllowed="true"/>
            </Property>
          </Properties>
          <AuxValues>
            <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
          </AuxValues>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="namaLabel">
      <Properties>
        <Property name="text" type="java.lang.String" value="Nama Produk"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="hargaLabel">
      <Properties>
        <Property name="text" type="java.lang.String" value="Harga"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="deskripsiLabel">
      <Properties>
        <Property name="text" type="java.lang.String" value="Deskripsi"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="stokLabel">
      <Properties>
        <Property name="text" type="java.lang.String" value="Stok"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="namaField">
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="hargaField">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="hargaFieldActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="deskripsiField">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="deskripsiFieldActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="stokField">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="stokFieldActionPerformed"/>
      </Events>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="tambahButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Tambah"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="simpanButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Simpan"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="editButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Edit"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="hapusButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Hapus"/>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="idLabel">
      <Properties>
        <Property name="text" type="java.lang.String" value="ID Produk"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="idField">
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
      </AuxValues>
    </Component>
  </SubComponents>
</Form>
