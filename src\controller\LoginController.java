package controller;
import model.LoginModel;
import java.util.ArrayList;

public class LoginController {
    private ArrayList<LoginModel> users = new ArrayList<>();

    public LoginController() {
        users.add(new LoginModel("admin", "admin123", "admin"));
        users.add(new LoginModel("kasir", "kasir123", "kasir"));
        users.add(new LoginModel("pemilik", "pemilik123", "pemilik"));
    }

    public LoginModel login(String username, String password) {
        for (LoginModel u : users) {
            if (u.getUsername().equals(username) && u.getPassword().equals(password)) {
                return u;
            }
        }
        return null;
    }
}
