/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package view;
import umkm_dimsry.BahanMain;
import umkm_dimsry.ProdukMain;
import umkm_dimsry.DashboardMain;
import umkm_dimsry.LaporanPenjualanMain;
/**
 *
 * <AUTHOR>
 */
public class DashboardPemilik extends javax.swing.JFrame {

    /**
     * Creates new form DashboardPemilik
     */
    public DashboardPemilik() {
        initComponents();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jPanel1 = new javax.swing.JPanel();
        pengelolaanProdukButton = new javax.swing.JButton();
        pengelolaanStokButton = new javax.swing.JButton();
        laporanPenjualanButton = new javax.swing.JButton();
        dashboardButton = new javax.swing.JButton();
        jLabel1 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        jLabel3 = new javax.swing.JLabel();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        pengelolaanProdukButton.setText("Pengelolaan Produk");
        pengelolaanProdukButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                pengelolaanProdukButtonActionPerformed(evt);
            }
        });

        pengelolaanStokButton.setText("Pengelolaan Bahan");
        pengelolaanStokButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                pengelolaanStokButtonActionPerformed(evt);
            }
        });

        laporanPenjualanButton.setText("Laporan Penjualan");
        laporanPenjualanButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                laporanPenjualanButtonActionPerformed(evt);
            }
        });

        dashboardButton.setText("Dashboard");
        dashboardButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                dashboardButtonActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout jPanel1Layout = new javax.swing.GroupLayout(jPanel1);
        jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addContainerGap()
                .addGroup(jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(pengelolaanProdukButton)
                    .addComponent(pengelolaanStokButton)
                    .addComponent(laporanPenjualanButton)
                    .addComponent(dashboardButton))
                .addContainerGap(17, Short.MAX_VALUE))
        );
        jPanel1Layout.setVerticalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addGap(63, 63, 63)
                .addComponent(pengelolaanProdukButton)
                .addGap(26, 26, 26)
                .addComponent(pengelolaanStokButton)
                .addGap(26, 26, 26)
                .addComponent(laporanPenjualanButton)
                .addGap(27, 27, 27)
                .addComponent(dashboardButton)
                .addContainerGap(161, Short.MAX_VALUE))
        );

        jLabel1.setText("Halo Admin, Selamat Datang di Dashboard UMKM Dimsry!");

        jLabel2.setText("Terima kasih sudah terus semangat membangun UMKM!");

        jLabel3.setText("Yuk, cek data produk, pantau transaksi, dan kelola usahamu dengan mudah. ");

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(jPanel1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(jLabel2)
                    .addComponent(jLabel3)
                    .addComponent(jLabel1))
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addComponent(jLabel1)
                .addGap(30, 30, 30)
                .addComponent(jLabel3)
                .addGap(37, 37, 37)
                .addComponent(jLabel2)
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
            .addComponent(jPanel1, javax.swing.GroupLayout.Alignment.TRAILING, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void pengelolaanProdukButtonActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_pengelolaanProdukButtonActionPerformed
        // TODO add your handling code here:
        ProdukMain.main(null);
    }//GEN-LAST:event_pengelolaanProdukButtonActionPerformed

    private void pengelolaanStokButtonActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_pengelolaanStokButtonActionPerformed
        // TODO add your handling code here:
        BahanMain.main(null);
    }//GEN-LAST:event_pengelolaanStokButtonActionPerformed

    private void dashboardButtonActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_dashboardButtonActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_dashboardButtonActionPerformed

    private void laporanPenjualanButtonActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_laporanPenjualanButtonActionPerformed
        // TODO add your handling code here:
        LaporanPenjualanMain.main(null);
    }//GEN-LAST:event_laporanPenjualanButtonActionPerformed

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(DashboardPemilik.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(DashboardPemilik.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(DashboardPemilik.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(DashboardPemilik.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new DashboardPemilik().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton dashboardButton;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JButton laporanPenjualanButton;
    private javax.swing.JButton pengelolaanProdukButton;
    private javax.swing.JButton pengelolaanStokButton;
    // End of variables declaration//GEN-END:variables
}
