package view;

import javax.swing.*;
import java.awt.*;
import umkm_dimsry.ProdukMain;
import umkm_dimsry.BahanMain;

public class AdminDashboard extends JFrame {

    public AdminDashboard() {
        setTitle("Dashboard Admin - UMKM DIMSRY");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(600, 400);
        setLocationRelativeTo(null);

        // Panel utama dengan sudut membulat
        JPanel mainPanel = new JPanel() {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2 = (Graphics2D) g;
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2.setColor(new Color(255, 255, 255));
                g2.fillRoundRect(0, 0, getWidth(), getHeight(), 30, 30);
            }
        };
        mainPanel.setBackground(new Color(245, 245, 245));
        mainPanel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(100, 149, 237), 2, true),
                BorderFactory.createEmptyBorder(32, 32, 32, 32)
        ));
        mainPanel.setLayout(new BorderLayout(0, 24));

        // Judul
        JLabel titleLabel = new JLabel("👑 Dashboard Admin", SwingConstants.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 28));
        titleLabel.setForeground(new Color(100, 149, 237));
        mainPanel.add(titleLabel, BorderLayout.NORTH);

        // Konten utama (bisa diganti sesuai kebutuhan)
        JPanel contentPanel = new JPanel();
        contentPanel.setOpaque(false);
        contentPanel.setLayout(new GridLayout(2, 2, 24, 24));

        JButton btnProduk = new JButton("Kelola Produk");
        JButton btnBahan = new JButton("Kelola Bahan Baku");
        JButton btnUser = new JButton("Kelola User");
        JButton btnLaporan = new JButton("Laporan Penjualan");

        for (JButton btn : new JButton[]{btnProduk, btnBahan, btnUser, btnLaporan}) {
            btn.setFont(new Font("Segoe UI", Font.PLAIN, 18));
            btn.setBackground(new Color(100, 149, 237));
            btn.setForeground(Color.WHITE);
            btn.setFocusPainted(false);
            btn.setBorder(BorderFactory.createLineBorder(new Color(100, 149, 237), 1, true));
            btn.setCursor(new Cursor(Cursor.HAND_CURSOR));
        }

        contentPanel.add(btnProduk);
        contentPanel.add(btnBahan);
        contentPanel.add(btnUser);
        contentPanel.add(btnLaporan);

        mainPanel.add(contentPanel, BorderLayout.CENTER);

        // Logout button
        JButton btnLogout = new JButton("Logout");
        btnLogout.setFont(new Font("Segoe UI", Font.BOLD, 16));
        btnLogout.setBackground(new Color(220, 53, 69));
        btnLogout.setForeground(Color.WHITE);
        btnLogout.setFocusPainted(false);
        btnLogout.setBorder(BorderFactory.createLineBorder(new Color(220, 53, 69), 1, true));
        btnLogout.setCursor(new Cursor(Cursor.HAND_CURSOR));
        btnLogout.addActionListener(e -> {
            dispose();
            new Login().setVisible(true);
        });

        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 0, 0));
        bottomPanel.setOpaque(false);
        bottomPanel.add(btnLogout);
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);

        setContentPane(mainPanel);

        String[] fitur = {"Pengelolaan Produk Dimsum", "Pengelolaan Stok dan Bahan", "Login"};
        String selected = (String) JOptionPane.showInputDialog(this, "Pilih fitur:", "Menu Admin",
                JOptionPane.QUESTION_MESSAGE, null, fitur, fitur[0]);

        if (selected != null) {
            dispose(); // Menutup dashboard
            switch (selected) {
                case "Pengelolaan Produk Dimsum":
                    ProdukMain.main(null); break;
                case "Pengelolaan Stok dan Bahan":
                    BahanMain.main(null); break;
                case "Login":
                    Login.main(null); break;
                default:
                    JOptionPane.showMessageDialog(null, "Fitur belum tersedia.");
            }
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> new AdminDashboard().setVisible(true));
    }
}
