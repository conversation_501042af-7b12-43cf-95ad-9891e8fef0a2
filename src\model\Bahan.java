package model;
import java.awt.HeadlessException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class <PERSON>han extends Database {

    private String idBahan;
    private String namaBahan;
    private int stok;
    private String satuan;

    // === Getter dan Setter ===
    public String getIdBahan() {
        return idBahan;
    }

    public void setIdBahan(String idBahan) {
        this.idBahan = idBahan;
    }

    public String getNamaBahan() {
        return namaBahan;
    }

    public void setNamaBahan(String namaBahan) {
        this.namaBahan = namaBahan;
    }

    public int getStok() {
        return stok;
    }

    public void setStok(int stok) {
        this.stok = stok;
    }

    public String getSatuan() {
        return satuan;
    }

    public void setSatuan(String satuan) {
        this.satuan = satuan;
    }

    // === CRUD Methods ===
    public boolean simpanBahan(Bahan data) throws SQLException {
        String sql = "INSERT INTO bahan_baku (id_bahan, nama_bahan, stok, satuan) VALUES (?, ?, ?, ?)";

        try (Connection conn = Database.configDB();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, data.getIdBahan());
            pstmt.setString(2, data.getNamaBahan());
            pstmt.setInt(3, data.getStok());
            pstmt.setString(4, data.getSatuan());
            pstmt.executeUpdate();
            return true;

        } catch (HeadlessException | SQLException e) {
            System.err.println("Gagal menyimpan bahan: " + e);
            return false;
        }
    }

    public boolean updateBahan(Bahan data) throws SQLException {
        String sql = "UPDATE bahan_baku SET nama_bahan = ?, stok = ?, satuan = ? WHERE id_bahan = ?";

        try (Connection conn = Database.configDB();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, data.getNamaBahan());
            pstmt.setInt(2, data.getStok());
            pstmt.setString(3, data.getSatuan());
            pstmt.setString(4, data.getIdBahan());
            pstmt.executeUpdate();
            return true;

        } catch (HeadlessException | SQLException e) {
            System.err.println("Gagal update bahan: " + e);
            return false;
        }
    }

    public boolean hapusBahan(Bahan data) throws SQLException {
        String sql = "DELETE FROM bahan_baku WHERE id_bahan = ?";

        try (Connection conn = Database.configDB();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, data.getIdBahan());
            pstmt.executeUpdate();
            return true;

        } catch (HeadlessException | SQLException e) {
            System.err.println("Gagal hapus bahan: " + e);
            return false;
        }
    }
}
