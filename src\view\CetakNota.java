/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package view;

/**
 *
 * <AUTHOR>
 */
public class CetakNota extends javax.swing.JFrame {

    /**
     * Creates new form CetakNota
     */
    public CetakNota() {
    initComponents(); // ini wajib duluan

    // Set model tabel dengan header yang benar
    notaTable.setModel(new javax.swing.table.DefaultTableModel(
        new Object[][]{},
        new String[]{"ID Produk", "Harga", "Jumlah", "Subtotal"}
    ));
}

    
    

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        CetakNotaText = new javax.swing.JLabel();
        namaField = new javax.swing.JLabel();
        TotalHargaField = new javax.swing.JLabel();
        alamatLabel = new javax.swing.JLabel();
        noHpLabel = new javax.swing.JLabel();
        trimakasiLabel = new javax.swing.JLabel();
        bukaLabel = new javax.swing.JLabel();
        dimsumLabel = new javax.swing.JLabel();
        jScrollPane1 = new javax.swing.JScrollPane();
        notaTable = new javax.swing.JTable();
        notaScrollPane = new javax.swing.JScrollPane();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        CetakNotaText.setFont(new java.awt.Font("Tahoma", 1, 24)); // NOI18N
        CetakNotaText.setText("Selamat Datang di DIMSRY");

        namaField.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        namaField.setText("Nama");

        TotalHargaField.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        TotalHargaField.setText("Harga Produk");

        alamatLabel.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        alamatLabel.setText("Jln. Garuda Sakti Km 03 Pekanbaru");

        noHpLabel.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        noHpLabel.setText("081397234856");

        trimakasiLabel.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        trimakasiLabel.setText("Terimakasih Telah Berbelanja di DIMSRY");

        bukaLabel.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        bukaLabel.setText("Buka : 09:00 - 21:00");

        dimsumLabel.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        dimsumLabel.setText("Dimsum paling enak no.1 se-Indonesia ");

        notaTable.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        notaTable.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null}
            },
            new String [] {
                "Title 1", "Title 2", "Title 3", "Title 4"
            }
        ));
        jScrollPane1.setViewportView(notaTable);

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                        .addComponent(TotalHargaField, javax.swing.GroupLayout.PREFERRED_SIZE, 121, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                .addContainerGap()
                                .addComponent(notaScrollPane, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                                .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 486, javax.swing.GroupLayout.PREFERRED_SIZE))
                            .addGroup(layout.createSequentialGroup()
                                .addGap(29, 29, 29)
                                .addComponent(namaField, javax.swing.GroupLayout.PREFERRED_SIZE, 117, javax.swing.GroupLayout.PREFERRED_SIZE))
                            .addGroup(layout.createSequentialGroup()
                                .addGap(104, 104, 104)
                                .addComponent(CetakNotaText))
                            .addGroup(layout.createSequentialGroup()
                                .addGap(156, 156, 156)
                                .addComponent(alamatLabel))
                            .addGroup(layout.createSequentialGroup()
                                .addGap(211, 211, 211)
                                .addComponent(noHpLabel))
                            .addGroup(layout.createSequentialGroup()
                                .addGap(154, 154, 154)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                                    .addComponent(trimakasiLabel)
                                    .addComponent(dimsumLabel)))))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(204, 204, 204)
                        .addComponent(bukaLabel)))
                .addContainerGap(31, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGap(244, 244, 244)
                        .addComponent(notaScrollPane, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                    .addGroup(layout.createSequentialGroup()
                        .addContainerGap()
                        .addComponent(CetakNotaText)
                        .addGap(18, 18, 18)
                        .addComponent(alamatLabel, javax.swing.GroupLayout.PREFERRED_SIZE, 16, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addGap(18, 18, 18)
                        .addComponent(noHpLabel)
                        .addGap(32, 32, 32)
                        .addComponent(namaField, javax.swing.GroupLayout.PREFERRED_SIZE, 31, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addGap(18, 18, 18)
                        .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 138, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addGap(18, 18, 18)
                        .addComponent(TotalHargaField)
                        .addGap(42, 42, 42)
                        .addComponent(trimakasiLabel)
                        .addGap(18, 18, 18)
                        .addComponent(bukaLabel)))
                .addGap(18, 18, 18)
                .addComponent(dimsumLabel)
                .addContainerGap(44, Short.MAX_VALUE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(CetakNota.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(CetakNota.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(CetakNota.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(CetakNota.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new CetakNota().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    public javax.swing.JLabel CetakNotaText;
    public javax.swing.JLabel TotalHargaField;
    public javax.swing.JLabel alamatLabel;
    public javax.swing.JLabel bukaLabel;
    public javax.swing.JLabel dimsumLabel;
    private javax.swing.JScrollPane jScrollPane1;
    public javax.swing.JLabel namaField;
    public javax.swing.JLabel noHpLabel;
    public javax.swing.JScrollPane notaScrollPane;
    public javax.swing.JTable notaTable;
    public javax.swing.JLabel trimakasiLabel;
    // End of variables declaration//GEN-END:variables
}
