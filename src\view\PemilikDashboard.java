package view;

import javax.swing.*;
import umkm_dimsry.*;

public class PemilikDashboard extends J<PERSON>rame {
    public PemilikDashboard() {
        setTitle("Dashboard Pemilik");
        setSize(400, 300);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        String[] fitur = {
            "Dashboard",
            "Laporan Harian",
            "Pengelolaan Data Pelanggan",
            "Pengelolaan Produk Dimsum",
            "Pengelolaan Stok dan Bahan"
        };

        String selected = (String) JOptionPane.showInputDialog(this, "Pilih fitur:", "Menu Pemilik",
                JOptionPane.QUESTION_MESSAGE, null, fitur, fitur[0]);

        if (selected != null) {
            dispose(); // tutup frame
            switch (selected) {
                case "Dashboard":
//                    DashboardMain.main(null); break;
                case "Laporan Harian":
                    LaporanPenjualanMain.main(null); break;
                case "Pengelolaan Data Pelanggan":
                    PelangganMain.main(null); break;
                case "Pengelolaan Produk Dimsum":
                    ProdukMain.main(null); break;
                case "Pengelolaan Stok dan Bahan":
                    BahanMain.main(null); break;
                default:
                    JOptionPane.showMessageDialog(null, "Fitur belum tersedia.");
            }
        }
    }
}
